// 只读Signal
interface ReadonlySignal<T> {
  (): T                           // 获取当前值
  subscribe(fn: (value: T) => void): () => void  // 订阅变化
}

// 可写Signal
interface WritableSignal<T> extends ReadonlySignal<T> {
  set(value: T): void            // 设置新值
  update(fn: (prev: T) => T): void  // 基于当前值更新
}

// 计算Signal
interface ComputedSignal<T> extends ReadonlySignal<T> {
  // 自动依赖追踪，无需手动订阅
}
// 创建可写Signal
function signal<T>(initialValue: T): WritableSignal<T>

// 创建计算Signal
function computed<T>(computeFn: () => T): ComputedSignal<T>

// 创建只读Signal
function readonly<T>(signal: WritableSignal<T>): ReadonlySignal<T>
// 批量更新，避免中间状态触发
function batch(fn: () => void): void

// 副作用处理
function effect(fn: () => void): () => void

// 条件Signal
function when<T>(
  condition: () => boolean, 
  trueFn: () => T, 
  falseFn: () => T
): ComputedSignal<T>

// 异步Signal
function asyncSignal<T>(
  asyncFn: () => Promise<T>, 
  initialValue: T
): WritableSignal<T>
// 基础用法
const count = signal(0)
const doubled = computed(() => count() * 2)

// 订阅变化
const unsubscribe = count.subscribe(value => console.log(value))

// 更新值
count.set(5)
count.update(prev => prev + 1)

// 副作用
const cleanup = effect(() => {
  console.log(`Count: ${count()}, Doubled: ${doubled()}`)
})

// 批量更新
batch(() => {
  count.set(10)
  // 其他更新...
})