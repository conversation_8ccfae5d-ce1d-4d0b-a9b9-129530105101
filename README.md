# Signal 机制 API 文档

一个轻量级、高性能的反应式状态管理库，使用 TypeScript 实现，无第三方依赖。

## 核心特性

- **反应式编程**：自动依赖追踪和更新
- **单向数据流**：数据变化方向明确
- **函数式设计**：纯函数计算，无副作用
- **类型安全**：完整的 TypeScript 支持
- **高性能**：批量更新，避免不必要的重复计算

## API 接口

### 基础类型

```typescript
// 只读Signal
interface ReadonlySignal<T> {
  get(): T                                    // 获取当前值
  subscribe(fn: (value: T) => void): () => void  // 订阅变化
}

// 可写Signal
interface WritableSignal<T> extends ReadonlySignal<T> {
  set(value: T): void                         // 设置新值
  update(fn: (prev: T) => T): void           // 基于当前值更新
}

// 计算Signal
interface ComputedSignal<T> extends ReadonlySignal<T> {
  // 自动依赖追踪，无需手动订阅
}
```

### 创建函数

#### `signal<T>(initialValue: T): WritableSignal<T>`
创建可写Signal

```typescript
const count = signal(0)
count.set(5)
count.update(prev => prev + 1)
console.log(count.get()) // 6
```

#### `computed<T>(computeFn: () => T): ComputedSignal<T>`
创建计算Signal，自动依赖追踪

```typescript
const doubled = computed(() => count.get() * 2)
console.log(doubled.get()) // 12
```

#### `readonly<T>(signal: WritableSignal<T>): ReadonlySignal<T>`
创建只读Signal包装器

```typescript
const readonlyCount = readonly(count)
// readonlyCount.set(10) // 编译错误
```

### 工具函数

#### `batch(fn: () => void): void`
批量更新，避免中间状态触发

```typescript
batch(() => {
  x.set(10)
  y.set(20)
  // 只会触发一次依赖更新
})
```

#### `effect(fn: () => void): () => void`
副作用处理，返回清理函数

```typescript
const cleanup = effect(() => {
  console.log(`Count: ${count.get()}`)
})
// cleanup() // 清理副作用
```

#### `when<T>(condition: () => boolean, trueFn: () => T, falseFn: () => T): ComputedSignal<T>`
条件Signal

```typescript
const status = when(
  () => age.get() >= 18,
  () => '成年人',
  () => '未成年'
)
```

#### `asyncSignal<T>(asyncFn: () => Promise<T>, initialValue: T): WritableSignal<T>`
异步Signal

```typescript
const data = asyncSignal(
  () => fetch('/api/data').then(r => r.json()),
  { loading: true }
)
```

## 使用示例

### 基础用法

```typescript
import { signal, computed, batch } from './signal'

// 创建状态
const firstName = signal('张')
const lastName = signal('三')

// 计算属性
const fullName = computed(() => `${firstName.get()}${lastName.get()}`)

// 订阅变化
const unsubscribe = fullName.subscribe(name => {
  console.log('姓名变化:', name)
})

// 批量更新
batch(() => {
  firstName.set('李')
  lastName.set('四')
}) // 只触发一次 fullName 变化

// 清理订阅
unsubscribe()
```

### 复杂状态管理

```typescript
// 用户状态
const user = signal({ name: '张三', age: 25 })
const isLoggedIn = signal(false)

// 计算属性
const userInfo = computed(() => {
  if (!isLoggedIn.get()) return '未登录'
  const u = user.get()
  return `${u.name} (${u.age}岁)`
})

// 条件显示
const greeting = when(
  () => isLoggedIn.get(),
  () => `欢迎，${user.get().name}！`,
  () => '请登录'
)

// 副作用
const cleanup = effect(() => {
  document.title = userInfo.get()
})
```

### 异步数据处理

```typescript
// API 数据
const apiData = asyncSignal(
  () => fetch('/api/users').then(r => r.json()),
  []
)

// 加载状态
const isLoading = computed(() => 
  Array.isArray(apiData.get()) && apiData.get().length === 0
)

// 错误处理
const errorMessage = signal('')

apiData.subscribe(data => {
  if (data.error) {
    errorMessage.set(data.error)
  }
})
```

## 性能特点

- **懒计算**：计算Signal只在被访问时才计算
- **缓存机制**：避免重复计算
- **批量更新**：减少不必要的中间状态
- **内存管理**：自动清理无用的依赖关系

## 最佳实践

1. **使用批量更新**：多个相关状态同时更新时使用 `batch()`
2. **及时清理订阅**：组件销毁时调用返回的清理函数
3. **避免循环依赖**：确保Signal之间的依赖关系是有向无环图
4. **合理使用计算Signal**：将复杂的派生状态抽取为计算Signal
5. **错误处理**：在计算函数中添加适当的错误处理

## 与其他方案对比

| 特性 | Signal | Redux | MobX | Zustand |
|------|--------|-------|------|---------|
| 包大小 | 极小 | 大 | 中等 | 小 |
| 学习成本 | 低 | 高 | 中等 | 低 |
| 类型安全 | 完整 | 需配置 | 部分 | 良好 |
| 性能 | 优秀 | 良好 | 优秀 | 良好 |
| 依赖追踪 | 自动 | 手动 | 自动 | 手动 |

这个Signal机制提供了现代反应式编程的所有核心功能，同时保持了简洁的API和优秀的性能。
